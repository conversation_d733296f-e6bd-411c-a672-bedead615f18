name: testflutter
description: "A new Flutter project."
publish_to: 'none'

version: 0.1.6

environment:
  sdk: '>=3.4.0 <4.0.0'

dependencies:
  flutter:
    sdk: flutter
  provider: ^6.0.5
  js: ^0.6.3
  shared_preferences:
    git:
      url: "https://gitee.com/openharmony-sig/flutter_packages.git"
      path: "packages/shared_preferences/shared_preferences"
  sqflite: ^2.2.8
  path: ^1.8.2
  uuid: ^4.5.1
  flutter_bloc: ^8.1.2
  equatable: ^2.0.5
  fluttertoast:
    git:
      url: "https://gitee.com/openharmony-sig/flutter_fluttertoast.git"
  intl: ^0.18.1
  chess: ^0.8.1
  stockfish_chess_engine:
    git:
      url: "https://github.com/iptton-ai/StockfishChessEngineFlutter.git"
  audioplayers: ^5.0.0

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^2.0.0
  mockito: ^5.3.2
  build_runner: ^2.3.3
  bloc_test: ^9.1.7
  
# Web specific configuration
web:
  csp:
    script-src: "'self' 'unsafe-eval' 'unsafe-inline' blob:"

flutter:
  uses-material-design: true

  assets:
    - assets/images/white_king.png
    - assets/images/white_queen.png
    - assets/images/white_bishop.png
    - assets/images/white_knight.png
    - assets/images/white_rook.png
    - assets/images/white_pawn.png
    - assets/images/black_king.png
    - assets/images/black_queen.png
    - assets/images/black_bishop.png
    - assets/images/black_knight.png
    - assets/images/black_pawn.png
    - assets/images/black_rook.png
    - assets/privacy/privacy.txt
    - assets/sounds/
